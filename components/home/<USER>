interface SkylineSvgProps {
  className?: string;
}

export default function SkylineSvg({ className }: SkylineSvgProps) {
  return (
    <svg
      id="Layer_2"
      data-name="Layer 2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1344.93 327.28"
      className={className}
    >
      <defs>
        <style>
          {`
            .cls-1 {
              fill: #fff;
            }
          `}
        </style>
      </defs>
      <g id="skyline_background" data-name="skyline background">
        <g>
          <path className="cls-1" d="M919.2,135.66c-2.77,1.33-8.87.45-8.89,3.79,2.18,56.72-1.31,113.77,0,170.47.09,3.96,5.89,1.45,8.89,2.1v-176.36ZM913.27,309.48v-169.15c0-.9,2.41-1.61,3.39-1.27v170.43c0,.17-3.27.12-3.39,0Z"/>
          <path className="cls-1" d="M900.55,135.66v176.36h8.48v-174.66c0-.43-7.15-.43-8.48-1.7ZM903.09,139.05c.56.04,3.39.67,3.39.85v169.58h-4.24c-.42-.31.85-1.09.85-1.27v-169.15Z"/>
          <path className="cls-1" d="M124.73,279.8h-5.09c-.3,3.7-1.87,27.81-.85,28.83.26.26,4.7-.31,6.21.15,1.9.58,1.84,2.92,2.85,3.25,5.54,1.85,1.36-5.2,1.06-8.01-.89-8.27-.61-21.32,2.61-28.86-.27-1.34-22.46-7.39-25.73-8.69l-25.99,8.25c-.9,2.04,1.42,1.36,1.68,2.26,2.05,7.06,1.17,24.43-.6,31.45-1.55,6.14,4.07,4.27,4.8-.28,1.45-8.99-.49-22.39-.73-31.67l20.85-6.65,20.12,6.48.58,1.36-1.78,16.54v-14.41Z"/>
          <path className="cls-1" d="M992.23,196.83l-55.6,105.07-.47,9.29c8.12.79,7.83-.19,10.91-6.48,16.78-34.31,30.37-71.02,47.21-105.41.82-2.56.77-3.01-2.04-2.46ZM938.7,308.63l.66-6.55,31.12-58.76,2.13-2.53-29.76,67.73-4.15.1Z"/>
          <path className="cls-1" d="M559.7,145.84c-10.85-2.8-5.2,8.6-6.78,10.18-.31.31-6.29-.23-7.64,0v12.72h-11.87v-12.72c-1.35-.23-7.32.32-7.64,0-.36-.36.27-8.58,0-10.18h-5.94v15.27c0,.38,7.63-.38,7.63,0v14.41h23.74v-14.41c0-.42,8.48.42,8.48,0v-15.26Z"/>
          <path className="cls-1" d="M1267.68,282.77c.17-.59,2.98-2.39,2.54-2.96-4.92-.19-15.99-7.89-19.86-7.63-4.33.28-13.83,7.36-19.14,7.63-.32.41,2.54,3.06,2.54,3.81v28.4h2.54v-30.1c3.22-1.34,11.73-6.27,14.71-6.11.67.03,11.85,4.38,12.61,4.86.6.37,1.51,1.6,1.51,2.1v29.25h2.54v-29.25Z"/>
          <path className="cls-1" d="M100.99,281.07c-.54-1.72-4.33-1.29-5.94-1.28.32,9.67-.24,19.24-.85,28.84h6.79v-27.56Z"/>
          <path className="cls-1" d="M117.1,281.07c-.54-1.72-4.33-1.29-5.94-1.28.21,9.66-.04,19.27-.85,28.84h6.79v-27.56Z"/>
          <polygon className="cls-1" points="103.53 279.8 102.68 308.64 109.47 308.64 108.63 279.8 103.53 279.8"/>
          <path className="cls-1" d="M86.58,308.63c9.37.61,6.07-1.32,5.9-7.17-.1-3.35.78-20.21-.75-21.29-1.25-.88-5.15-.36-5.15.9v27.56Z"/>
          <path className="cls-1" d="M533.41,132.27v22.05h12.72v-22.05h-12.72ZM535.96,134.81h7.63v16.96h-7.63v-16.96Z"/>
        </g>
      </g>
    </svg>
  );
}
