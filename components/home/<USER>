interface TitleSvgProps {
  className?: string;
}

export default function TitleSvg({ className }: TitleSvgProps) {
  return (
    <svg
      id="Layer_2"
      data-name="Layer 2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 797.77 518.64"
      className={className}
    >
      <defs>
        <style>
          {`
            .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6 {
              font-family: var(--font-league-gothic), 'League Gothic', Arial, sans-serif;
              font-variation-settings: 'wdth' 100;
            }

            .cls-1, .cls-3, .cls-5 {
              fill: #de1ace;
            }

            .cls-1, .cls-4 {
              font-size: 138.96px;
            }

            .cls-7 {
              font-family: var(--font-alex-brush), 'Alex Brush', cursive;
              font-size: 52.07px;
            }

            .cls-7, .cls-2, .cls-4, .cls-6 {
              fill: #fcade7;
            }

            .cls-2, .cls-5 {
              font-size: 372.77px;
            }

            .cls-3, .cls-6 {
              font-size: 263.47px;
            }
          `}
        </style>
      </defs>
      <g id="presents">
        <g>
          <g>
            <text className="cls-5" transform="translate(11.02 425.45)">
              <tspan x="0" y="0">F</tspan>
            </text>
            <text className="cls-3" transform="translate(121.73 318.15)">
              <tspan x="0" y="0">O</tspan>
            </text>
            <text className="cls-1" transform="translate(206.83 219.08)">
              <tspan x="0" y="0">R</tspan>
            </text>
            <text className="cls-1" transform="translate(260.32 219.08)">
              <tspan x="0" y="0">EVER</tspan>
            </text>
            <text className="cls-1" transform="translate(500.2 219.08)">
              <tspan x="0" y="0">FE</tspan>
            </text>
            <text className="cls-3" transform="translate(580.68 320.26)">
              <tspan x="0" y="0">S</tspan>
            </text>
            <text className="cls-5" transform="translate(674.02 425.45)">
              <tspan x="0" y="0">T</tspan>
            </text>
          </g>
          <g>
            <text className="cls-2" transform="translate(0 410.88)">
              <tspan x="0" y="0">F</tspan>
            </text>
            <text className="cls-6" transform="translate(110.71 303.58)">
              <tspan x="0" y="0">O</tspan>
            </text>
            <text className="cls-4" transform="translate(195.81 204.51)">
              <tspan x="0" y="0">R</tspan>
            </text>
            <text className="cls-4" transform="translate(249.3 204.51)">
              <tspan x="0" y="0">EVER</tspan>
            </text>
            <text className="cls-4" transform="translate(489.18 204.51)">
              <tspan x="0" y="0">FE</tspan>
            </text>
            <text className="cls-6" transform="translate(569.66 305.69)">
              <tspan x="0" y="0">S</tspan>
            </text>
            <text className="cls-2" transform="translate(663.01 410.87)">
              <tspan x="0" y="0">T</tspan>
            </text>
          </g>
          <text className="cls-7" transform="translate(304.3 57.41) scale(.84 1)">
            <tspan x="0" y="0">Presents</tspan>
          </text>
        </g>
      </g>
    </svg>
  );
}
