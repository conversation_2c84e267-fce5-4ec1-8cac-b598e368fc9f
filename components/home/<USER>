interface SeanAndEvaSkylineSvgProps {
  className?: string;
}

export default function SeanAndEvaSkylineSvg({ className }: SeanAndEvaSkylineSvgProps) {
  return (
    <svg
      id="Layer_2"
      data-name="Layer 2"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 301.27 126.83"
      className={className}
    >
      <defs>
        <style>
          {`
            .cls-1, .cls-2 {
              fill: #fdf0da;
            }

            .cls-3 {
              letter-spacing: 0em;
            }

            .cls-4 {
              letter-spacing: -.04em;
            }

            .cls-2 {
              font-family: var(--font-league-gothic), 'League Gothic', Arial, sans-serif;
              font-size: 49.64px;
              font-variation-settings: 'wdth' 100;
            }
          `}
        </style>
      </defs>
      <g id="site_logo" data-name="site logo">
        <g>
          <g>
            <path className="cls-1" d="M205.9,30.39c-.62.3-1.99.1-1.99.85.49,12.71-.29,25.49,0,38.19.02.89,1.32.33,1.99.47V30.39ZM204.57,69.32V31.43c0-.2.54-.36.76-.28v38.18s-.73.03-.76,0Z"/>
            <path className="cls-1" d="M201.73,30.39v39.51h1.9V30.77c0-.1-1.6-.1-1.9-.38ZM202.3,31.15c.13,0,.76.15.76.19v37.99h-.95c-.09-.07.19-.24.19-.28V31.15Z"/>
            <path className="cls-1" d="M27.94,62.68h-1.14c-.07.83-.42,6.23-.19,6.46.06.06,1.05-.07,1.39.03.43.13.41.65.64.73,1.24.41.3-1.16.24-1.79-.2-1.85-.14-4.78.58-6.47-.06-.3-5.03-1.65-5.76-1.95l-5.82,1.85c-.2.46.32.3.38.51.46,1.58.26,5.47-.13,7.05-.35,1.38.91.96,1.08-.06.33-2.01-.11-5.02-.16-7.09l4.67-1.49,4.51,1.45.13.3-.4,3.71v-3.23Z"/>
            <path className="cls-1" d="M222.26,44.09l-12.46,23.54-.11,2.08c1.82.18,1.75-.04,2.44-1.45,3.76-7.68,6.8-15.91,10.58-23.61.18-.57.17-.67-.46-.55ZM210.27,69.13l.15-1.47,6.97-13.16.48-.57-6.67,15.17-.93.02Z"/>
            <path className="cls-1" d="M125.37,32.67c-2.43-.63-1.16,1.93-1.52,2.28-.07.07-1.41-.05-1.71,0v2.85h-2.66v-2.85c-.3-.05-1.64.07-1.71,0-.08-.08.06-1.92,0-2.28h-1.33v3.42c0,.09,1.71-.09,1.71,0v3.23h5.32v-3.23c0-.09,1.9.09,1.9,0v-3.42Z"/>
            <path className="cls-1" d="M283.97,63.34c.04-.13.67-.54.57-.66-1.1-.04-3.58-1.77-4.45-1.71-.97.06-3.1,1.65-4.29,1.71-.07.09.57.69.57.85v6.36h.57v-6.74c.72-.3,2.63-1.4,3.29-1.37.15,0,2.65.98,2.82,1.09.13.08.34.36.34.47v6.55h.57v-6.55Z"/>
            <path className="cls-1" d="M22.62,62.96c-.12-.39-.97-.29-1.33-.29.07,2.17-.05,4.31-.19,6.46h1.52v-6.17Z"/>
            <path className="cls-1" d="M26.23,62.96c-.12-.39-.97-.29-1.33-.29.05,2.16,0,4.32-.19,6.46h1.52v-6.17Z"/>
            <polygon className="cls-1" points="23.19 62.68 23 69.14 24.52 69.14 24.33 62.68 23.19 62.68"/>
            <path className="cls-1" d="M19.39,69.13c2.1.14,1.36-.29,1.32-1.61-.02-.75.17-4.53-.17-4.77-.28-.2-1.15-.08-1.15.2v6.17Z"/>
            <path className="cls-1" d="M119.49,29.63v4.94h2.85v-4.94h-2.85ZM120.06,30.2h1.71v3.8h-1.71v-3.8Z"/>
          </g>
          <text className="cls-2" transform="translate(70.71 114.42)">
            <tspan x="0" y="0">S</tspan>
            <tspan className="cls-3" x="16.78" y="0">E</tspan>
            <tspan x="33.11" y="0">AN &amp; E</tspan>
            <tspan className="cls-4" x="124.56" y="0">V</tspan>
            <tspan x="140.54" y="0">A</tspan>
          </text>
        </g>
      </g>
    </svg>
  );
}
